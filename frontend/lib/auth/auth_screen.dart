import 'package:best_flutter_ui_templates/yourai/yourai_home_screen.dart';
import 'package:best_flutter_ui_templates/utils/login_status.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:openapi/openapi.dart';
import 'package:provider/provider.dart';
import 'package:best_flutter_ui_templates/yourai/yourai_theme.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:best_flutter_ui_templates/constants.dart';

class AuthScreen extends StatefulWidget {
  const AuthScreen({super.key});
  @override
  State<AuthScreen> createState() => _AuthScreenState();
}

class _AuthScreenState extends State<AuthScreen> {
  final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: [
      'email',
    ],
  );
  final Openapi _openapi = Openapi();
  late final GoogleApi _googleApi;
  late final LoginApi _loginApi;
  bool _isSigningIn = false;
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isPasswordVisible = false;

  @override
  void initState() {
    super.initState();
    _googleApi = _openapi.getGoogleApi();
    _loginApi = _openapi.getLoginApi();
  }

  Future<void> _loginWithPassword() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isSigningIn = true;
      });

      try {
        final response = await _loginApi.loginLoginAccessToken(
          grantType: 'password',
          username: _usernameController.text,
          password: _passwordController.text,
        );
        _handleLoginSuccess(response);
      } on DioException catch (e) {
        _handleLoginError(e);
      } catch (e) {
        _handleLoginError(e);
      } finally {
        setState(() {
          _isSigningIn = false;
        });
      }
    }
  }

  Future<void> _login() async {
    setState(() {
      _isSigningIn = true;
    });

    try {
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser != null) {
        final GoogleSignInAuthentication googleAuth =
            await googleUser.authentication;
        final response = await _googleApi.googleGoogleCallback(
          headers: {
            'Authorization': 'Bearer ${googleAuth.accessToken}',
          },
        );
        _handleLoginSuccess(response);
      }
    } on DioException catch (e) {
      _handleLoginError(e);
    } catch (e) {
      _handleLoginError(e);
    } finally {
      setState(() {
        _isSigningIn = false;
      });
    }
  }

  void _handleLoginSuccess(Response<dynamic> response) async {
    // TODO: Handle the response from the backend
    print(response.data);

    // Extract access token from response
    String? accessToken;
    if (response.data is Token) {
      accessToken = (response.data as Token).accessToken;
    }

    if (accessToken != null) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(Constants.authTokenKey, accessToken);
    }

    Provider.of<LoginStatus>(context, listen: false)
        .login(accessToken: accessToken);
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => YourAIHomeScreen(),
      ),
    );
  }

  void _handleLoginError(dynamic e) {
    print('Error during Google login: $e');
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('An error occurred during login: $e'),
        backgroundColor: YourAITheme.pinkAccent,
      ),
    );
  }

  void _loginWithoutGoogle() {
    _usernameController.text = '<EMAIL>';
    _passwordController.text = 'Ph7eeesM.m7WteT4Cy-M';
    _loginWithPassword();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: YourAITheme.background,
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                'YourAI',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontFamily: YourAITheme.fontName,
                  fontSize: 48,
                  fontWeight: FontWeight.bold,
                  color: YourAITheme.darkerText,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Welcome back!',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontFamily: YourAITheme.fontName,
                  fontSize: 18,
                  color: YourAITheme.darkText,
                ),
              ),
              const SizedBox(height: 48),
              _usernameField(),
              const SizedBox(height: 16),
              _passwordField(),
              const SizedBox(height: 24),
              _isSigningIn
                  ? const Center(child: CircularProgressIndicator())
                  : _loginButton(),
              const SizedBox(height: 16),
              _googleLoginButton(),
              const SizedBox(height: 16),
              _loginWithoutGoogleButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _usernameField() {
    return TextFormField(
      style: TextStyle(
        fontFamily: YourAITheme.fontName,
        color: YourAITheme.darkerText,
      ),
      controller: _usernameController,
      decoration: InputDecoration(
        labelText: 'Username or Email',
        labelStyle: TextStyle(
          fontFamily: YourAITheme.fontName,
          color: YourAITheme.darkText,
        ),
        prefixIcon: Icon(Icons.person_outline, color: YourAITheme.darkText),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: YourAITheme.darkText),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: YourAITheme.nearlyDarkBlue),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter your username or email';
        }
        return null;
      },
    );
  }

  Widget _passwordField() {
    return TextFormField(
      style: TextStyle(
        fontFamily: YourAITheme.fontName,
        color: YourAITheme.darkerText,
      ),
      controller: _passwordController,
      obscureText: !_isPasswordVisible,
      decoration: InputDecoration(
        labelText: 'Password',
        labelStyle: TextStyle(
          fontFamily: YourAITheme.fontName,
          color: YourAITheme.darkText,
        ),
        prefixIcon: Icon(Icons.lock_outline, color: YourAITheme.darkText),
        suffixIcon: IconButton(
          icon: Icon(
            _isPasswordVisible ? Icons.visibility : Icons.visibility_off,
            color: YourAITheme.darkText,
          ),
          onPressed: () {
            setState(() {
              _isPasswordVisible = !_isPasswordVisible;
            });
          },
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: YourAITheme.darkText),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: YourAITheme.nearlyDarkBlue),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter your password';
        }
        return null;
      },
    );
  }

  Widget _loginButton() {
    return SizedBox(
      height: 48,
      child: ElevatedButton(
        onPressed: _loginWithPassword,
        style: ElevatedButton.styleFrom(
          backgroundColor: YourAITheme.nearlyDarkBlue,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Text(
          'Login',
          style: TextStyle(
            fontFamily: YourAITheme.fontName,
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  Widget _googleLoginButton() {
    return SizedBox(
      height: 48,
      child: OutlinedButton.icon(
        onPressed: _login,
        icon: const FaIcon(
          FontAwesomeIcons.google,
          color: Colors.white,
          size: 20,
        ),
        label: Text(
          'Continue with Google',
          style: TextStyle(
            fontFamily: YourAITheme.fontName,
            fontSize: 16,
            color: YourAITheme.darkerText,
          ),
        ),
        style: OutlinedButton.styleFrom(
          side: BorderSide(color: YourAITheme.darkText),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  Widget _loginWithoutGoogleButton() {
    return TextButton(
      onPressed: _loginWithoutGoogle,
      child: Text(
        'Login with demo account',
        style: TextStyle(
          fontFamily: YourAITheme.fontName,
          color: YourAITheme.darkText,
          decoration: TextDecoration.underline,
        ),
      ),
    );
  }
}