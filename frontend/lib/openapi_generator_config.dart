// Openapi Generator last run: : 2025-07-06T13:21:58.570814
import 'package:openapi_generator_annotations/openapi_generator_annotations.dart';

@Openapi(
  additionalProperties: DioProperties(
    pubName: 'api_client',
    pubAuthor: 'Best Flutter UI Templates',
  ),
  inputSpec: RemoteSpec(path: 'http://localhost:8000/api/v1/openapi.json'),
  generatorName: Generator.dioNext,
  runSourceGenOnOutput: false,
  outputDirectory: 'api_client',
)
class OpenApiGeneratorConfig {}