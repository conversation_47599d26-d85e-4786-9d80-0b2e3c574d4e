#!/usr/bin/env bash

# OpenAPI Client Generator Script
# This script generates a Dart API client from an OpenAPI specification
# using the openapi_generator package with proper Flutter integration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
FRONTEND_DIR="frontend"
API_BASE_URL="${API_BASE_URL:-http://localhost:8000}"
OPENAPI_ENDPOINT="${API_BASE_URL}/api/v1/openapi.json"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if API server is running
check_api_server() {
    print_status "Checking if API server is running at ${API_BASE_URL}..."
    if curl -s --fail "${OPENAPI_ENDPOINT}" >/dev/null; then
        print_success "API server is running"
        return 0
    else
        print_error "API server is not running at ${API_BASE_URL}"
        print_warning "Please start your backend server first"
        return 1
    fi
}

# Function to validate dependencies
validate_dependencies() {
    print_status "Validating dependencies..."

    if ! command_exists flutter; then
        print_error "Flutter is not installed or not in PATH"
        exit 1
    fi

    if ! command_exists curl; then
        print_error "curl is not installed"
        exit 1
    fi

    print_success "All dependencies are available"
}

# Function to clean previous generation
clean_previous_generation() {
    print_status "Cleaning previous generation..."

    cd "${FRONTEND_DIR}"

    # Remove existing api_client directory
    if [ -d "api_client" ]; then
        rm -rf api_client
        print_success "Removed existing api_client directory"
    fi

    # Remove existing openapi.json
    if [ -f "openapi.json" ]; then
        rm -f openapi.json
        print_success "Removed existing openapi.json"
    fi
}

# Function to generate API client
generate_client() {
    print_status "Generating API client..."

    # Use build_runner to generate the client
    flutter pub run build_runner build --delete-conflicting-outputs

    if [ $? -eq 0 ]; then
        print_success "API client generated successfully"
    else
        print_error "Failed to generate API client"
        exit 1
    fi
}

# Function to verify generation
verify_generation() {
    print_status "Verifying generated client..."

    if [ ! -d "api_client" ]; then
        print_error "api_client directory was not created"
        exit 1
    fi

    if [ ! -f "api_client/pubspec.yaml" ]; then
        print_error "Generated client is missing pubspec.yaml"
        exit 1
    fi

    print_success "Generated client verification passed"
}

# Function to install dependencies
install_dependencies() {
    print_status "Installing dependencies..."

    # Install main project dependencies
    flutter pub get

    # Install generated client dependencies
    if [ -d "api_client" ]; then
        cd api_client
        flutter pub get
        cd ..
    fi

    print_success "Dependencies installed successfully"
}

# Main execution
main() {
    print_status "Starting OpenAPI client generation..."

    # Validate environment
    validate_dependencies

    # Check if API server is running
    if ! check_api_server; then
        exit 1
    fi

    # Clean previous generation
    clean_previous_generation

    # Generate client
    generate_client

    # Verify generation
    verify_generation

    # Install dependencies
    install_dependencies

    print_success "OpenAPI client generation completed successfully!"
    print_status "Generated client is available in the 'api_client' directory"
}

# Run main function
main "$@"