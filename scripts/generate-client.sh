#! /usr/bin/env bash

set -e
set -x

cd frontend

# Comment out openapi dependency temporarily
sed -i '' 's/^\( *openapi:\)/#\1/' pubspec.yaml
sed -i '' 's/^\( *path: .\/api_client\)/#\1/' pubspec.yaml

flutter pub get

# Download openapi.json
curl -X GET "http://localhost:8000/api/v1/openapi.json" > openapi.json

# Remove existing api_client directory
rm -rf api_client

# Generate client using openapi-generator CLI
openapi-generator generate -i openapi.json -g dart-dio -o api_client

# Run pub get in generated api_client
cd api_client
flutter pub get
cd ..

# Uncomment openapi dependency
sed -i '' 's/^#\( *openapi:\)/\1/' pubspec.yaml
sed -i '' 's/^#\( *path: .\/api_client\)/\1/' pubspec.yaml

flutter pub get