
# Build configuration for openapi_generator
# This file configures the build_runner to generate API clients

targets:
  $default:
    builders:
      openapi_generator:
        enabled: true
        options:
          # Use remote spec directly - no need for local file
          input_spec: "http://localhost:8000/api/v1/openapi.json"
          generator: "dio-next"
          output_directory: "api_client"
          run_source_gen_on_output: true
          # Additional options for better generation
          additional_properties:
            nullableFields: true
            serialization: "json_annotation"
            dateLibrary: "core"
            enumUnknownDefaultCase: true
          # Skip validation for faster builds during development
          skip_spec_validation: false
          # Template directory for custom templates (if needed)
          # template_dir: "templates"
        generate_for:
          - lib/openapi_generator_config.dart

  # Optional: Configure other builders if needed
  # json_serializable:
  #   options:
  #     explicit_to_json: true
  #     include_if_null: false
